#!/bin/bash

# Test Password Risk Data Generation Script
# Usage: ./scripts/test-password-risk.sh [--dry-run] [--help]

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$(dirname "$SCRIPT_DIR")"

show_help() {
    echo "Test Password Risk Data Generation Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --dry-run    Generate test data without saving to database"
    echo "  --help       Show this help message"
    echo ""
    echo "Environment Variables Required (unless --dry-run):"
    echo "  SECRET_CRYPTOGRAPHY_PASSWORD  - Encryption password"
    echo "  SECRET_CRYPTOGRAPHY_SALT      - Encryption salt"
    echo ""
    echo "Optional Environment Variables:"
    echo "  HOST                          - Base URL (default: http://localhost:3000)"
    echo "  PATH_PREFIX                   - URL path prefix (default: empty)"
    echo ""
    echo "Examples:"
    echo "  $0 --dry-run                  # Test without saving to database"
    echo "  $0                            # Generate and save test data"
    echo ""
}

# Parse command line arguments
DRY_RUN=false
for arg in "$@"; do
    case $arg in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $arg"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Change to backend directory
cd "$BACKEND_DIR"

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Error: Node.js is not installed or not in PATH"
    exit 1
fi

# Check if the test script exists
if [ ! -f "commands/test_password_risk.js" ]; then
    echo "❌ Error: test_password_risk.js not found in commands directory"
    exit 1
fi

echo "🚀 Starting Password Risk Test Data Generation..."
echo ""

# Run the script
if [ "$DRY_RUN" = true ]; then
    echo "🔍 Running in DRY-RUN mode..."
    node commands/test_password_risk.js --dry-run
else
    echo "💾 Running in PRODUCTION mode..."
    node commands/test_password_risk.js
fi

echo ""
echo "🎉 Script completed successfully!"
