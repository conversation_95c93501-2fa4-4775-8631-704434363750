import crypto from 'crypto';
import { COLLECTION_HIBP, COLLECTION_REGULARLY_PASSWORD_CONFIGURATION } from '../constants/collections.js';
import { THIRTY_DAYS_MILLISECOND, TTL_MILLISECOND } from '../constants/constants.js';
import { saveDoc } from '../providers/firestore.js';
import { encrypt, hashEmail } from '../services/cryptography.js';

const createUniqueEmail = (keyName) => {
  const uuid = crypto.randomUUID();
  return `example-${keyName}-${uuid}@gmo.jp`;
};

const DUMMY = {
  // No leak scenario
  NO_LEAK: {
    createdAt: '2025-01-30T11:40:02.133Z',
    expiredAt: '2025-02-13T11:40:01.722Z',
    result: [],
  },

  // Single verified breach with passwords
  LEAK_SINGLE_VERIFIED: {
    createdAt: '2025-01-30T11:41:10.017Z',
    expiredAt: '2025-02-13T11:41:09.701Z',
    result: [
      {
        Name: 'Peatix',
        Title: 'Peatix',
        Domain: 'peatix.com',
        BreachDate: '2019-01-20',
        AddedDate: '2020-12-06T22:53:53Z',
        ModifiedDate: '2020-12-06T22:53:53Z',
        PwnCount: 4227907,
        Description:
          'In January 2019, the event organising platform Peatix suffered a data breach.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/Peatix.png',
        DataClasses: ['Email addresses', 'Names', 'Passwords'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },

  // Single unverified breach with passwords
  LEAK_SINGLE_UNVERIFIED: {
    createdAt: '2025-01-30T11:42:15.123Z',
    expiredAt: '2025-02-13T11:42:14.890Z',
    result: [
      {
        Name: 'Collection1',
        Title: 'Collection #1',
        Domain: '',
        BreachDate: '2019-01-07',
        AddedDate: '2019-01-16T21:46:07Z',
        ModifiedDate: '2019-01-16T21:50:21Z',
        PwnCount: *********,
        Description:
          'In January 2019, a large collection of credential stuffing lists was discovered being distributed on a popular hacking forum.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/List.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: false,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },
  // Breach without passwords (should not count)
  LEAK_NO_PASSWORDS: {
    createdAt: '2025-01-30T11:43:20.456Z',
    expiredAt: '2025-02-13T11:43:20.123Z',
    result: [
      {
        Name: 'Apollo',
        Title: 'Apollo',
        Domain: 'apollo.io',
        BreachDate: '2018-07-23',
        AddedDate: '2018-10-05T19:14:11Z',
        ModifiedDate: '2018-10-23T04:01:48Z',
        PwnCount: 125929660,
        Description:
          'In July 2018, the sales engagement startup Apollo left a database containing billions of data points publicly exposed without a password.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/Apollo.png',
        DataClasses: [
          'Email addresses',
          'Employers',
          'Geographic locations',
          'Job titles',
          'Names',
          'Phone numbers',
          'Salutations',
          'Social media profiles',
        ],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },

  // Sensitive breach with passwords
  LEAK_SENSITIVE_VALID: {
    createdAt: '2025-01-31T05:25:33.735Z',
    expiredAt: '2999-12-31T23:59:59.000Z',
    result: [
      {
        Name: 'AdultFriendFinder',
        Title: 'Adult FriendFinder',
        Domain: 'adultfriendfinder.com',
        BreachDate: '2016-10-01',
        AddedDate: '2016-11-14T00:00:00Z',
        ModifiedDate: '2016-11-14T00:00:00Z',
        PwnCount: *********,
        Description:
          'In October 2016, the adult website Adult FriendFinder was hacked and over 400 million accounts were exposed.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/AdultFriendFinder.png',
        DataClasses: ['Dates of birth', 'Email addresses', 'Ethnicities', 'Genders', 'Names', 'Passwords', 'Phone numbers', 'Sexual orientations', 'Usernames'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: true,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },

  // Spam list breach with passwords
  LEAK_SPAM_LIST_VALID: {
    createdAt: '2025-01-31T05:25:33.735Z',
    expiredAt: '2999-12-31T23:59:59.000Z',
    result: [
      {
        Name: 'Collection1',
        Title: 'Collection #1',
        Domain: '',
        BreachDate: '2019-01-07',
        AddedDate: '2019-01-16T21:46:07Z',
        ModifiedDate: '2019-01-16T21:46:07Z',
        PwnCount: *********,
        Description:
          'In January 2019, a large collection of credential stuffing lists was discovered being distributed on a popular hacking forum.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/List.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: false,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: true,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },

  // Fabricated breach with passwords
  LEAK_FABRICATED: {
    createdAt: '2025-01-31T05:26:45.890Z',
    expiredAt: '2025-02-14T05:26:45.567Z',
    result: [
      {
        Name: 'FakeBreachExample',
        Title: 'Fake Breach Example',
        Domain: 'fakesite.com',
        BreachDate: '2020-01-01',
        AddedDate: '2020-06-01T00:00:00Z',
        ModifiedDate: '2020-06-01T00:00:00Z',
        PwnCount: 1000000,
        Description:
          'This is an example of a fabricated breach that was not legitimate.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/Default.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: false,
        IsFabricated: true,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },

  // Retired breach with passwords
  LEAK_RETIRED: {
    createdAt: '2025-01-31T05:27:30.234Z',
    expiredAt: '2025-02-14T05:27:29.901Z',
    result: [
      {
        Name: 'RetiredBreachExample',
        Title: 'Retired Breach Example',
        Domain: 'retiredsite.com',
        BreachDate: '2015-01-01',
        AddedDate: '2015-06-01T00:00:00Z',
        ModifiedDate: '2015-06-01T00:00:00Z',
        PwnCount: 500000,
        Description:
          'This is an example of a retired breach that is no longer actively monitored.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/Default.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: true,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },

  // Multiple breaches with mixed properties
  LEAK_MULTIPLE_MIXED: {
    createdAt: '2025-01-31T05:25:33.735Z',
    expiredAt: '2025-02-14T05:25:33.090Z',
    result: [
      {
        Name: 'Nihonomaru',
        Title: 'Nihonomaru',
        Domain: 'nihonomaru.net',
        BreachDate: '2015-12-01',
        AddedDate: '2016-08-30T09:54:55Z',
        ModifiedDate: '2016-08-30T09:54:55Z',
        PwnCount: 1697282,
        Description:
          'In late 2015, the anime community known as Nihonomaru had their vBulletin forum hacked and 1.7 million accounts exposed.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/Nihonomaru.png',
        DataClasses: [
          'Email addresses',
          'IP addresses',
          'Passwords',
          'Usernames',
        ],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Dropbox',
        Title: 'Dropbox',
        Domain: 'dropbox.com',
        BreachDate: '2012-07-01',
        AddedDate: '2016-08-31T00:19:19Z',
        ModifiedDate: '2016-08-31T00:19:19Z',
        PwnCount: ********,
        Description:
          'In mid-2012, Dropbox suffered a data breach which exposed the stored credentials of tens of millions of their customers.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/Dropbox.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Apollo',
        Title: 'Apollo',
        Domain: 'apollo.io',
        BreachDate: '2018-07-23',
        AddedDate: '2018-10-05T19:14:11Z',
        ModifiedDate: '2018-10-23T04:01:48Z',
        PwnCount: 125929660,
        Description:
          'In July 2018, the sales engagement startup Apollo left a database containing billions of data points publicly exposed.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/Apollo.png',
        DataClasses: [
          'Email addresses',
          'Employers',
          'Geographic locations',
          'Job titles',
          'Names',
          'Phone numbers',
        ],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Collection1',
        Title: 'Collection #1',
        Domain: '',
        BreachDate: '2019-01-07',
        AddedDate: '2019-01-16T21:46:07Z',
        ModifiedDate: '2019-01-16T21:50:21Z',
        PwnCount: *********,
        Description:
          'In January 2019, a large collection of credential stuffing lists was discovered being distributed on a popular hacking forum.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/List.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: false,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: true,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },

  // Malware-related breach with passwords
  LEAK_MALWARE: {
    createdAt: '2025-01-31T05:28:15.678Z',
    expiredAt: '2025-02-14T05:28:15.345Z',
    result: [
      {
        Name: 'MalwareExample',
        Title: 'Malware Example',
        Domain: 'malwaresite.com',
        BreachDate: '2020-06-01',
        AddedDate: '2020-12-01T00:00:00Z',
        ModifiedDate: '2020-12-01T00:00:00Z',
        PwnCount: 750000,
        Description:
          'This is an example of a breach caused by malware that collected user credentials.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/AnimeGame.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: true,
        IsSubscriptionFree: false,
      },
    ],
  },

  // Subscription-free breach with passwords
  LEAK_SUBSCRIPTION_FREE: {
    createdAt: '2025-01-31T05:29:00.123Z',
    expiredAt: '2025-02-14T05:28:59.890Z',
    result: [
      {
        Name: 'FreeServiceExample',
        Title: 'Free Service Example',
        Domain: 'freeservice.com',
        BreachDate: '2021-03-01',
        AddedDate: '2021-09-01T00:00:00Z',
        ModifiedDate: '2021-09-01T00:00:00Z',
        PwnCount: 2000000,
        Description:
          'This is an example of a breach from a free service that does not require subscription.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/List.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: true,
      },
      {
        Name: 'Peatix',
        Title: 'Peatix',
        Domain: 'peatix.com',
        BreachDate: '2019-01-20',
        AddedDate: '2020-12-06T22:53:53Z',
        ModifiedDate: '2020-12-06T22:53:53Z',
        PwnCount: 4227907,
        Description:
          'In January 2019, the event organising platform Peatix suffered a data breach.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/Peatix.png',
        DataClasses: ['Email addresses', 'Names', 'Passwords'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },

  // Multiple breaches with valid expiration
  LEAK_MULTIPLE_VALID: {
    createdAt: '2025-01-31T05:25:33.735Z',
    expiredAt: '2999-12-31T23:59:59.000Z',
    result: [
      {
        Name: 'Adobe',
        Title: 'Adobe',
        Domain: 'adobe.com',
        BreachDate: '2013-10-04',
        AddedDate: '2013-12-04T00:00:00Z',
        ModifiedDate: '2013-12-04T00:00:00Z',
        PwnCount: *********,
        Description:
          'In October 2013, 153 million Adobe accounts were breached.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/Adobe.png',
        DataClasses: ['Email addresses', 'Password hints', 'Passwords', 'Usernames'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Dropbox',
        Title: 'Dropbox',
        Domain: 'dropbox.com',
        BreachDate: '2012-07-01',
        AddedDate: '2016-08-31T00:19:19Z',
        ModifiedDate: '2016-08-31T00:19:19Z',
        PwnCount: ********,
        Description:
          'In mid-2012, Dropbox suffered a data breach which exposed stored credentials.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/Dropbox.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },

  // Expired sensitive breach
  LEAK_SENSITIVE_EXPIRED: {
    createdAt: '2025-01-31T05:25:33.735Z',
    expiredAt: '2024-12-31T23:59:59.000Z',
    result: [
      {
        Name: 'AdultFriendFinder',
        Title: 'Adult FriendFinder',
        Domain: 'adultfriendfinder.com',
        BreachDate: '2016-10-01',
        AddedDate: '2016-11-14T00:00:00Z',
        ModifiedDate: '2016-11-14T00:00:00Z',
        PwnCount: *********,
        Description:
          'In October 2016, the adult website Adult FriendFinder was hacked and over 400 million accounts were exposed.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/AdultFriendFinder.png',
        DataClasses: ['Dates of birth', 'Email addresses', 'Ethnicities', 'Genders', 'Names', 'Passwords', 'Phone numbers', 'Sexual orientations', 'Usernames'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: true,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },
};

const configurations = {
  noRegularNoNoti: {
    isRegularly: false,
    interval: 1,
    isNotification: false,
    nextCheckedAt: null,
  },
  pastDate: {
    isRegularly: true,
    interval: 3,
    isNotification: true,
    nextCheckedAt: new Date(Date.parse('2025-03-10') - THIRTY_DAYS_MILLISECOND).toISOString(),
  },
  todayDate: {
    isRegularly: true,
    interval: 3,
    isNotification: false,
    nextCheckedAt: new Date().toISOString(),
  },
  futureDate: {
    isRegularly: false,
    interval: 6,
    isNotification: false,
    nextCheckedAt: new Date(new Date().getTime() + THIRTY_DAYS_MILLISECOND).toISOString(),
  },
  thresholdDate: {
    isRegularly: true,
    interval: 1,
    isNotification: true,
    nextCheckedAt: new Date(Date.parse('2025-03-10')).toISOString(),
  },
  regularInterval1: {
    isRegularly: true,
    interval: 1,
    isNotification: true,
    nextCheckedAt: new Date(new Date().getTime() + THIRTY_DAYS_MILLISECOND).toISOString(),
  },
  regularInterval6: {
    isRegularly: true,
    interval: 6,
    isNotification: false,
    nextCheckedAt: new Date(new Date().getTime() + (6 * THIRTY_DAYS_MILLISECOND)).toISOString(),
  },
  noRegularWithInterval: {
    isRegularly: false,
    interval: 3,
    isNotification: true,
    nextCheckedAt: null,
  },
};

const run = async () => {
  for (const configKey in configurations) {
    const configuration = configurations[configKey];

    for (const keyName in DUMMY) {
      const dataHibp = { ...DUMMY[keyName] };
      const uniqueEmail = createUniqueEmail(keyName);
      dataHibp.email = uniqueEmail;

      const now = new Date();
      const isExpired = keyName.includes('EXPIRED');
      const expiredAt = isExpired ? new Date('2024-12-31T23:59:59.000Z') : new Date(now.getTime() + TTL_MILLISECOND);
      const hashedEmail = await hashEmail(uniqueEmail);

      const code = await encrypt(
        JSON.stringify({ email: uniqueEmail, expiredAt: expiredAt.getTime() }),
        process.env.SECRET_CRYPTOGRAPHY_PASSWORD,
        process.env.SECRET_CRYPTOGRAPHY_SALT,
      );

      const names = dataHibp.result
        .filter(r => r.DataClasses.includes('Passwords'))
        .map(r => r.Name);

      const count = names.length;

      const hibpData = {
        collection: COLLECTION_HIBP,
        docId: code,
        data: {
          ...dataHibp,
          configuration,
        },
      };

      const configData = {
        collection: COLLECTION_REGULARLY_PASSWORD_CONFIGURATION,
        docId: hashedEmail,
        data: {
          encryptedEmail: uniqueEmail,
          isRegularly: configuration.isRegularly,
          interval: configuration.interval,
          nextCheckedAt: configuration.nextCheckedAt,
          isNotification: configuration.isNotification,
          history: [{
            createdAt: dataHibp.createdAt,
            code: code,
            names: names,
            count: count,
          }],
        },
      };

      await saveDoc(hibpData);
      await saveDoc(configData);

      console.log({
        email: uniqueEmail,
        createdAt: dataHibp.createdAt,
        expiredAt: dataHibp.expiredAt,
        type: keyName,
        configType: configKey,
        configuration,
        expired: isExpired,
        leakedSites: names.join(', ') || 'None',
        leakCount: count,
        url: `${process.env.HOST}${process.env.PATH_PREFIX}/check/password/?code=${code}`,
        hibpDocId: hibpData.docId,
        configDocId: configData.docId,
      });
    }
  }
};
run();
