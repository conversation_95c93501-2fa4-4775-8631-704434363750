# Password Risk Test Data Generator

このスクリプトは、パスワードリスクシステムの包括的なテストデータを生成します。

## 概要

`test_password_risk.js` は、HIBP（Have I Been Pwned）データベースとパスワード設定データベースに、すべてのパスワードリスクパターンをカバーするテストデータを生成します。

### 生成されるテストパターン

#### 漏洩パターン (13種類)
- `NO_LEAK`: 漏洩なし
- `LEAK_SINGLE_VERIFIED`: 単一の検証済み漏洩
- `LEAK_SINGLE_UNVERIFIED`: 単一の未検証漏洩
- `LEAK_NO_PASSWORDS`: パスワードデータクラスを含まない漏洩
- `LEAK_SENSITIVE_VALID`: 有効な機密漏洩
- `LEAK_SPAM_LIST_VALID`: 有効なスパムリスト漏洩
- `LEAK_FABRICATED`: 偽造漏洩
- `LEAK_RETIRED`: 廃止漏洩
- `LEAK_MULTIPLE_MIXED`: 混合プロパティの複数漏洩
- `LEAK_MALWARE`: マルウェア関連漏洩
- `LEAK_SUBSCRIPTION_FREE`: サブスクリプション無料漏洩
- `LEAK_MULTIPLE_VALID`: 複数の有効漏洩
- `LEAK_SENSITIVE_EXPIRED`: 期限切れ機密漏洩

#### 設定パターン (8種類)
- `noRegularNoNoti`: 定期チェックなし、通知なし
- `pastDate`: 過去の日付
- `todayDate`: 今日の日付
- `futureDate`: 未来の日付
- `thresholdDate`: 閾値日付
- `regularInterval1`: 1ヶ月間隔の定期チェック
- `regularInterval6`: 6ヶ月間隔の定期チェック
- `noRegularWithInterval`: 定期チェックなし（間隔設定あり）

**合計**: 13 × 8 = 104 テストレコード

## 使用方法

### 1. 直接実行

```bash
# ドライランモード（データベースに保存しない）
node commands/test_password_risk.js --dry-run

# 本番モード（データベースに保存）
node commands/test_password_risk.js
```

### 2. ヘルパースクリプト使用

```bash
# ドライランモード
./scripts/test-password-risk.sh --dry-run

# 本番モード
./scripts/test-password-risk.sh

# ヘルプ表示
./scripts/test-password-risk.sh --help
```

## 環境変数

### 必須（本番モードの場合）
- `SECRET_CRYPTOGRAPHY_PASSWORD`: 暗号化パスワード
- `SECRET_CRYPTOGRAPHY_SALT`: 暗号化ソルト

### オプション
- `HOST`: ベースURL（デフォルト: http://localhost:3000）
- `PATH_PREFIX`: URLパスプレフィックス（デフォルト: 空）

## 出力例

```json
{
  "email": "<EMAIL>",
  "createdAt": "2025-01-31T05:25:33.735Z",
  "expiredAt": "2999-12-31T23:59:59.000Z",
  "type": "LEAK_SENSITIVE_VALID",
  "configType": "regularInterval1",
  "configuration": {
    "isRegularly": true,
    "interval": 1,
    "isNotification": true,
    "nextCheckedAt": "2025-02-01T00:00:00.000Z"
  },
  "expired": false,
  "leakedSites": "AdultFriendFinder",
  "leakCount": 1,
  "url": "http://localhost:3000/check/password/?code=...",
  "hibpDocId": "encrypted-code",
  "configDocId": "<EMAIL>",
  "saved": true
}
```

## 注意事項

1. **ドライランモード**: テスト用の暗号化キーを使用するため、生成されたURLは本番環境では動作しません
2. **本番モード**: 実際の環境変数を使用するため、生成されたURLは本番環境で動作します
3. **データベース**: 本番モードでは実際にFirestoreにデータが保存されます

## トラブルシューティング

### "Invalid code" エラー
- 暗号化キーが本番環境と一致していない可能性があります
- 環境変数が正しく設定されているか確認してください

### "Loading" 状態が続く
- データベースにデータが保存されていない可能性があります
- 本番モードでスクリプトを実行してください

### 環境変数エラー
- 必要な環境変数が設定されていません
- ドライランモードで動作確認を行ってください
